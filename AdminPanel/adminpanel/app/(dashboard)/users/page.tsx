"use client"

import { useState, useCallback } from "react"
import { DashboardHeader } from "@/components/dashboard-header"
import { DashboardShell } from "@/components/dashboard-shell"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Search, RefreshCw } from "lucide-react"
import { UsersList } from "@/components/users-list"
import { Button } from "@/components/ui/button"

export default function UsersPage() {
  const [activeTab, setActiveTab] = useState("all")
  const [searchQuery, setSearchQuery] = useState("")
  const [refreshKey, setRefreshKey] = useState(0)

  // Handle tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value)
  }

  // Handle search
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value)
  }

  // Handle refresh
  const handleRefresh = useCallback(() => {
    setRefreshKey(prev => prev + 1)
  }, [])

  return (
    <DashboardShell>
      <DashboardHeader heading="User Management" text="Manage users and account approvals">
        <div className="flex items-center gap-2">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search users..."
              className="w-[200px] pl-8 md:w-[300px]"
              value={searchQuery}
              onChange={handleSearch}
            />
          </div>
          <Button variant="outline" size="icon" onClick={handleRefresh}>
            <RefreshCw className="h-4 w-4" />
          </Button>
        </div>
      </DashboardHeader>

      <Tabs value={activeTab} onValueChange={handleTabChange} className="space-y-4">
        <TabsList>
          <TabsTrigger value="all">All Users</TabsTrigger>
          <TabsTrigger value="new">New Requests</TabsTrigger>
        </TabsList>
        <TabsContent value="all" className="space-y-4">
          <UsersList key={`all-${refreshKey}`} type="all" onRefresh={handleRefresh} />
        </TabsContent>
        <TabsContent value="new" className="space-y-4">
          <UsersList key={`new-${refreshKey}`} type="new" onRefresh={handleRefresh} />
        </TabsContent>
      </Tabs>
    </DashboardShell>
  )
}
