import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import AuthLayout from "./auth-layout"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "Kraft Paper Admin Dashboard",
  description: "Admin dashboard for kraft paper business",
  generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <AuthLayout>{children}</AuthLayout>
      </body>
    </html>
  )
}
