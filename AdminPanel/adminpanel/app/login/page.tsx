"use client";

import React, { useState } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { Box, Eye, EyeOff } from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useAuth } from '@/lib/context/auth-context';

// Form validation schema
const loginSchema = z.object({
  email: z.string().email({ message: 'Please enter a valid email address' }),
  password: z.string().min(6, { message: 'Password must be at least 6 characters' }),
});

type LoginFormValues = z.infer<typeof loginSchema>;

export default function LoginPage() {
  const { login, sendPasswordResetEmail, isLoading } = useAuth();
  const [error, setError] = useState<string | null>(null);
  const [isResetMode, setIsResetMode] = useState<boolean>(false);
  const [resetEmail, setResetEmail] = useState<string>('');
  const [resetSuccess, setResetSuccess] = useState<boolean>(false);
  const [showPassword, setShowPassword] = useState<boolean>(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  const onSubmit = async (data: LoginFormValues) => {
    setError(null);
    try {
      console.log('Login form submitted');
      await login(data.email, data.password);

      // Check if the token was stored in localStorage
      const authToken = localStorage.getItem('auth_token');
      const userData = localStorage.getItem('user_data');
      
      console.log('Auth token after login:', !!authToken);
      console.log('User data in localStorage:', !!userData);

    } catch (err) {
      console.error('Login form error:', err);
      setError('Login failed. Please check your credentials.');
    }
  };

  // Handle password reset request
  const handleResetPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setResetSuccess(false);

    if (!resetEmail) {
      setError('Please enter your email address');
      return;
    }

    try {
      await sendPasswordResetEmail(resetEmail);
      setResetSuccess(true);
    } catch (err) {
      console.error('Password reset error:', err);
      setError('Failed to send password reset email. Please try again.');
    }
  };

  // Toggle between login and reset password modes
  const toggleResetMode = () => {
    setIsResetMode(!isResetMode);
    setError(null);
    setResetSuccess(false);
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-muted/40 p-4">
      <Card className="mx-auto max-w-md w-full">
        <CardHeader className="space-y-1">
          <div className="flex items-center justify-center mb-2">
            <Box className="h-10 w-10 text-primary" />
          </div>
          <CardTitle className="text-2xl text-center">
            {isResetMode ? 'Reset Password' : 'Admin Login'}
          </CardTitle>
          <CardDescription className="text-center">
            {isResetMode
              ? 'Enter your email to receive a password reset link'
              : 'Enter your credentials to access the admin dashboard'}
          </CardDescription>
        </CardHeader>

        {isResetMode ? (
          // Password Reset Form
          <form onSubmit={handleResetPassword}>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="reset-email">Email</Label>
                <Input
                  id="reset-email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={resetEmail}
                  onChange={(e) => setResetEmail(e.target.value)}
                />
              </div>

              {error && (
                <div className="rounded-md bg-destructive/15 p-3 text-sm text-destructive">
                  {error}
                </div>
              )}

              {resetSuccess && (
                <div className="rounded-md bg-green-100 p-3 text-sm text-green-800">
                  Password reset email sent. Please check your inbox.
                </div>
              )}
            </CardContent>
            <CardFooter className="flex flex-col space-y-2">
              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? 'Sending...' : 'Send Reset Link'}
              </Button>
              <Button
                type="button"
                variant="ghost"
                className="w-full"
                onClick={toggleResetMode}
              >
                Back to Login
              </Button>
            </CardFooter>
          </form>
        ) : (
          // Login Form
          <form onSubmit={handleSubmit(onSubmit)}>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  {...register('email')}
                />
                {errors.email && (
                  <p className="text-sm text-destructive">{errors.email.message}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    placeholder="••••••••"
                    {...register('password')}
                  />
                  <button
                    type="button"
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 focus:outline-none"
                    onClick={() => setShowPassword(!showPassword)}
                    tabIndex={-1}
                    aria-label={showPassword ? "Hide password" : "Show password"}
                  >
                    {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                  </button>
                </div>
                {errors.password && (
                  <p className="text-sm text-destructive">{errors.password.message}</p>
                )}
              </div>
              {error && (
                <div className="rounded-md bg-destructive/15 p-3 text-sm text-destructive">
                  {error}
                </div>
              )}
            </CardContent>
            <CardFooter className="flex flex-col space-y-2">
              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? 'Logging in...' : 'Login'}
              </Button>
              <Button
                type="button"
                variant="ghost"
                className="w-full"
                onClick={toggleResetMode}
              >
                Forgot Password?
              </Button>
            </CardFooter>
          </form>
        )}
      </Card>
    </div>
  );
}
