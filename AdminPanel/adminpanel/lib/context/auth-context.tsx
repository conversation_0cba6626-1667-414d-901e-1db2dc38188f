"use client"

import type React from "react"
import { create<PERSON>ontext, useContext, useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { toast } from "sonner"
import { authService } from "@/lib/services/authService"
import type { User } from "@/lib/types"
import analyticsService from "@/lib/analytics/analytics-service"

interface AuthContextType {
  user: User | null
  isLoading: boolean
  isAuthenticated: boolean
  login: (email: string, password: string) => Promise<void>
  logout: () => Promise<void>
  sendPasswordResetEmail: (email: string) => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState<boolean>(true)
  const router = useRouter()

  // Check if user is already logged in
  useEffect(() => {
    const checkAuth = async () => {
      try {
        // Check Firebase authentication first
        const firebaseUser = await firebaseAuthService.getCurrentUser()

        if (firebaseUser) {
          // Get the Firebase ID token
          const idToken = await firebaseUser.getIdToken(true) // Force refresh

          // Get the user data from localStorage if available
          const userDataStr = localStorage.getItem("user_data")

          if (userDataStr) {
            try {
              const userData = JSON.parse(userDataStr)
              // Set user data from localStorage
              setUser({
                ...userData,
                firebaseUser,
                idToken, // Update token with fresh one
              })

              // Update the token in localStorage
              localStorage.setItem("auth_token", idToken)
              console.log("Auth token updated in localStorage during auth check")

              // Track user login in analytics
              analyticsService.init(userData.localId)

              // Set up token refresh interval - refresh token every 55 minutes
              const tokenRefreshInterval = setInterval(
                async () => {
                  try {
                    const freshUser = await firebaseAuthService.getCurrentUser()
                    if (freshUser) {
                      const newToken = await freshUser.getIdToken(true)
                      localStorage.setItem("auth_token", newToken)
                      console.log("Auth token refreshed")
                    } else {
                      clearInterval(tokenRefreshInterval)
                    }
                  } catch (error) {
                    console.error("Token refresh error:", error)
                    // Handle token refresh failure
                    handleTokenRefreshFailure()
                  }
                },
                55 * 60 * 1000,
              ) // 55 minutes

              // Store interval id in sessionStorage to clear it on logout
              sessionStorage.setItem("token_refresh_interval", String(tokenRefreshInterval))
            } catch (error) {
              console.error("Error parsing user data from localStorage:", error)
              await firebaseAuthService.signOut()
              setUser(null)
            }
          } else {
            // No user data in localStorage, sign out
            await firebaseAuthService.signOut()
            setUser(null)
          }
        } else {
          setUser(null)
        }
      } catch (error) {
        console.error("Error checking authentication:", error)
        setUser(null)
      } finally {
        setIsLoading(false)
      }
    }

    checkAuth()
  }, [])

  // Handle token refresh failure
  const handleTokenRefreshFailure = () => {
    // Clear user data
    setUser(null)
    localStorage.removeItem("auth_token")
    localStorage.removeItem("user_data")

    // Show toast notification
    toast.error("Your session has expired. Please log in again.", {
      duration: 5000,
      onAutoClose: () => {
        // Redirect to login page
        router.replace("/login")
      },
    })
  }

  // Refresh token manually
  const refreshToken = async (): Promise<boolean> => {
    try {
      const firebaseUser = await firebaseAuthService.getCurrentUser()
      if (firebaseUser) {
        const newToken = await firebaseUser.getIdToken(true)
        localStorage.setItem("auth_token", newToken)

        // Update user state with new token
        if (user) {
          setUser({
            ...user,
            idToken: newToken,
          })
        }

        return true
      }
      return false
    } catch (error) {
      console.error("Manual token refresh error:", error)
      handleTokenRefreshFailure()
      return false
    }
  }

  // Login function
  const login = async (email: string, password: string) => {
    setIsLoading(true)
    try {
      console.log("Attempting login with Firebase:", email)

      // Sign in with Firebase
      const userCredential = await firebaseAuthService.signIn(email, password)
      const firebaseUser = userCredential.user

      // Get the Firebase ID token and user data
      const idToken = await firebaseUser.getIdToken(true) // Force refresh
      const refreshToken = userCredential.user.refreshToken
      const localId = userCredential.user.uid
      const displayName = userCredential.user.displayName || "Admin"

      // Create user object from Firebase response
      const userData: User = {
        localId,
        email,
        idToken,
        refreshToken,
        displayName,
        expiresIn: "3600", // Default expiry of 1 hour
        firebaseUser,
      }

      // Store user data and token
      localStorage.setItem("user_data", JSON.stringify(userData))
      localStorage.setItem("auth_token", idToken)

      // Add a flag to prevent redirect loop
      sessionStorage.setItem("manual_redirect", "true")
      console.log("Auth token set in localStorage")

      // Track user login in analytics
      analyticsService.init(localId)
      analyticsService.trackEvent("user_login", "login_page", undefined, { method: "email" })

      // Set up token refresh interval - refresh token every 55 minutes
      const tokenRefreshInterval = setInterval(
        async () => {
          try {
            const freshUser = await firebaseAuthService.getCurrentUser()
            if (freshUser) {
              const newToken = await freshUser.getIdToken(true)
              localStorage.setItem("auth_token", newToken)
              console.log("Auth token refreshed")
            } else {
              clearInterval(tokenRefreshInterval)
            }
          } catch (error) {
            console.error("Token refresh error:", error)
            handleTokenRefreshFailure()
          }
        },
        55 * 60 * 1000,
      ) // 55 minutes

      // Store interval id in sessionStorage to clear it on logout
      sessionStorage.setItem("token_refresh_interval", String(tokenRefreshInterval))

      // Set user in state
      setUser(userData)

      console.log("User set in context, redirecting to dashboard")
      toast.success("Login successful")

      // Add a small delay before redirecting to ensure state is updated
      setTimeout(() => {
        console.log("Redirecting to dashboard page")
        router.replace("/dashboard")

        // Clear the manual redirect flag after a delay
        setTimeout(() => {
          sessionStorage.removeItem("manual_redirect")
        }, 1000)
      }, 500)
    } catch (error) {
      console.error("Login error:", error)
      toast.error("Login failed. Please check your credentials.")
      analyticsService.trackError("Login failed")
      throw error // Re-throw to allow the login page to handle the error
    } finally {
      setIsLoading(false)
    }
  }

  // Logout function
  const logout = async () => {
    setIsLoading(true)
    try {
      // Track logout event
      if (user) {
        analyticsService.trackEvent("user_logout", "app", undefined, { userId: user.localId })
      }

      // Clear token refresh interval if exists
      const intervalId = sessionStorage.getItem("token_refresh_interval")
      if (intervalId) {
        clearInterval(Number.parseInt(intervalId))
        sessionStorage.removeItem("token_refresh_interval")
      }

      // Sign out from Firebase
      await firebaseAuthService.signOut()

      // Clear data from localStorage
      localStorage.removeItem("auth_token")
      localStorage.removeItem("user_data")

      // Add a flag to prevent redirect loop
      sessionStorage.setItem("manual_redirect", "true")
      console.log("Auth token cleared from localStorage")

      // Clear user state
      setUser(null)
      toast.success("Logout successful")

      // Navigate to login page
      router.replace("/login")

      // Clear the manual redirect flag after a delay
      setTimeout(() => {
        sessionStorage.removeItem("manual_redirect")
      }, 1000)
    } catch (error) {
      console.error("Logout error:", error)
      toast.error("Logout failed")
      analyticsService.trackError("Logout failed")
    } finally {
      setIsLoading(false)
    }
  }

  // Send password reset email
  const sendPasswordResetEmail = async (email: string) => {
    try {
      await firebaseAuthService.sendPasswordResetEmail(email)
      toast.success("Password reset email sent. Please check your inbox.")
      analyticsService.trackEvent("password_reset_request", "login_page", undefined, { email })
    } catch (error) {
      console.error("Password reset error:", error)
      toast.error("Failed to send password reset email. Please try again.")
      analyticsService.trackError("Password reset failed")
      throw error
    }
  }

  const value = {
    user,
    isLoading,
    isAuthenticated: !!user,
    login,
    logout,
    sendPasswordResetEmail,
    refreshToken,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}
