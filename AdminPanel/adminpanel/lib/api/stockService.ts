export interface StockItem {
  id: string
  type: string
  gsm: number
  bf: number
  rollsAvailable: number
  immediatePrice: number
  thirtyDayPrice: number
  sixtyDayPrice: number
  createdAt: string
  updatedAt: string
}

export interface CreateStockItemDto {
  type: string
  gsm: number
  bf: number
  rollsAvailable: number
  immediatePrice: number
  thirtyDayPrice: number
  sixtyDayPrice: number
}

export interface UpdateStockItemDto {
  rollsAvailable?: number
  immediatePrice?: number
  thirtyDayPrice?: number
  sixtyDayPrice?: number
}

export interface StockQueryParams {
  page?: number
  limit?: number
  search?: string
  type?: string
  gsm?: number
  bf?: number
  startDate?: string
  endDate?: string
  sortBy?: string
  sortOrder?: "asc" | "desc"
}

interface StockResponse {
  items: StockItem[]
  pagination: {
    totalItems: number
    totalPages: number
    currentPage: number
    itemsPerPage: number
  }
}

const stockService = {
  getAllStockItems: async (params: StockQueryParams = {}): Promise<StockResponse> => {
    try {
      // In a real app, this would be an API call
      // const response = await apiClient.get('/stock', { params });
      // return response.data;

      // For demo purposes, we'll return mock data
      return {
        items: mockStockItems,
        pagination: {
          totalItems: mockStockItems.length,
          totalPages: 1,
          currentPage: params.page || 1,
          itemsPerPage: params.limit || 10,
        },
      }
    } catch (error) {
      console.error("Error fetching stock items:", error)
      throw error
    }
  },

  getStockItemById: async (id: string): Promise<StockItem> => {
    try {
      // In a real app, this would be an API call
      // const response = await apiClient.get(`/stock/${id}`);
      // return response.data;

      // For demo purposes, we'll return mock data
      const item = mockStockItems.find((item) => item.id === id)
      if (!item) {
        throw new Error("Stock item not found")
      }
      return item
    } catch (error) {
      console.error(`Error fetching stock item with ID ${id}:`, error)
      throw error
    }
  },

  createStockItem: async (stockItem: CreateStockItemDto): Promise<StockItem> => {
    try {
      // In a real app, this would be an API call
      // const response = await apiClient.post('/stock', stockItem);
      // return response.data;

      // For demo purposes, we'll return mock data
      const newItem: StockItem = {
        id: `stock-${Date.now()}`,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        ...stockItem,
      }
      mockStockItems.push(newItem)
      return newItem
    } catch (error) {
      console.error("Error creating stock item:", error)
      throw error
    }
  },

  updateStockItem: async (id: string, stockItem: UpdateStockItemDto): Promise<StockItem> => {
    try {
      // In a real app, this would be an API call
      // const response = await apiClient.put(`/stock/${id}`, stockItem);
      // return response.data;

      // For demo purposes, we'll update mock data
      const index = mockStockItems.findIndex((item) => item.id === id)
      if (index === -1) {
        throw new Error("Stock item not found")
      }

      mockStockItems[index] = {
        ...mockStockItems[index],
        ...stockItem,
        updatedAt: new Date().toISOString(),
      }

      return mockStockItems[index]
    } catch (error) {
      console.error(`Error updating stock item with ID ${id}:`, error)
      throw error
    }
  },

  deleteStockItem: async (id: string): Promise<void> => {
    try {
      // In a real app, this would be an API call
      // await apiClient.delete(`/stock/${id}`);

      // For demo purposes, we'll update mock data
      const index = mockStockItems.findIndex((item) => item.id === id)
      if (index === -1) {
        throw new Error("Stock item not found")
      }

      mockStockItems.splice(index, 1)
    } catch (error) {
      console.error(`Error deleting stock item with ID ${id}:`, error)
      throw error
    }
  },

  importStockItems: async (items: CreateStockItemDto[]): Promise<StockItem[]> => {
    try {
      // In a real app, this would be an API call
      // const response = await apiClient.post('/stock/import', { items });
      // return response.data;

      // For demo purposes, we'll update mock data
      const newItems = items.map((item) => ({
        id: `stock-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        ...item,
      }))

      mockStockItems.push(...newItems)
      return newItems
    } catch (error) {
      console.error("Error importing stock items:", error)
      throw error
    }
  },

  exportStockItems: async (): Promise<StockItem[]> => {
    try {
      // In a real app, this would be an API call
      // const response = await apiClient.get('/stock/export');
      // return response.data;

      // For demo purposes, we'll return mock data
      return mockStockItems
    } catch (error) {
      console.error("Error exporting stock items:", error)
      throw error
    }
  },

  importStockFromCSV: async (file: File): Promise<{ success: boolean; imported?: number; message?: string; errors?: any[] }> => {
    try {
      // In a real app, this would be an API call with FormData
      // const formData = new FormData();
      // formData.append('file', file);
      // const response = await apiClient.post('/stock/import', formData);
      // return response.data;

      // For demo purposes, simulate CSV import
      return {
        success: true,
        imported: 5,
        message: "Stock items imported successfully"
      }
    } catch (error) {
      console.error("Error importing stock from CSV:", error)
      return {
        success: false,
        message: "Failed to import stock from CSV"
      }
    }
  },
}

// Mock data for demonstration
const mockStockItems: StockItem[] = [
  {
    id: "stock-1",
    type: "NS",
    gsm: 80,
    bf: 16,
    rollsAvailable: 50,
    immediatePrice: 120.0,
    thirtyDayPrice: 126.0,
    sixtyDayPrice: 132.0,
    createdAt: "2023-01-10T08:15:00Z",
    updatedAt: "2023-05-15T10:30:00Z",
  },
  {
    id: "stock-2",
    type: "GY",
    gsm: 100,
    bf: 20,
    rollsAvailable: 75,
    immediatePrice: 150.0,
    thirtyDayPrice: 157.5,
    sixtyDayPrice: 165.0,
    createdAt: "2023-02-05T11:30:00Z",
    updatedAt: "2023-05-20T14:45:00Z",
  },
  {
    id: "stock-3",
    type: "NS",
    gsm: 120,
    bf: 18,
    rollsAvailable: 30,
    immediatePrice: 180.0,
    thirtyDayPrice: 189.0,
    sixtyDayPrice: 198.0,
    createdAt: "2023-01-25T13:45:00Z",
    updatedAt: "2023-05-18T09:15:00Z",
  },
  {
    id: "stock-4",
    type: "GY",
    gsm: 140,
    bf: 22,
    rollsAvailable: 25,
    immediatePrice: 220.0,
    thirtyDayPrice: 231.0,
    sixtyDayPrice: 242.0,
    createdAt: "2023-03-12T10:00:00Z",
    updatedAt: "2023-05-22T16:30:00Z",
  },
  {
    id: "stock-5",
    type: "NS",
    gsm: 200,
    bf: 25,
    rollsAvailable: 15,
    immediatePrice: 300.0,
    thirtyDayPrice: 315.0,
    sixtyDayPrice: 330.0,
    createdAt: "2023-02-18T09:30:00Z",
    updatedAt: "2023-05-17T11:20:00Z",
  },
]

export default stockService
