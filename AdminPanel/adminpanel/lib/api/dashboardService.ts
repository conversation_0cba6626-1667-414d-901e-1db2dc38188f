import apiClient, { ApiResponse } from './apiClient';

// Types
export type Period = 'last7days' | 'last30days' | 'last90days' | 'lastYear' | 'allTime';

export interface SalesOverview {
  totalSales: number;
  totalOrders: number;
  averageOrderValue: number;
  pendingOrders: number;
  completedOrders: number;
  cancelledOrders: number;
  monthlySales: Array<{
    month: string;
    revenue: number;
    orders: number;
  }>;
}

export interface TopProduct {
  id: string;
  name: string;
  gsm: number;
  size: string;
  totalSold: number;
  totalRevenue: number;
  percentageOfSales: number;
}

export interface CustomerStats {
  totalCustomers: number;
  newCustomers: number;
  activeCustomers: number;
  customerGrowth: number;
  topCustomers: Array<{
    id: string;
    companyName: string;
    totalSpent: number;
    orderCount: number;
  }>;
}

export interface InventoryStats {
  totalStockItems: number;
  inventoryValue: number;
  lowStockItems: number;
  stockItems: Array<{
    id: string;
    name: string;
    gsm: number;
    size: string;
    quantity: number;
    price: number;
    value: number;
    status: 'IN_STOCK' | 'LOW_STOCK' | 'OUT_OF_STOCK';
  }>;
}

export interface DashboardStats {
  salesOverview: SalesOverview;
  topProducts: TopProduct[];
  customerStats: CustomerStats;
  inventoryStats: InventoryStats;
}

/**
 * Dashboard Service
 */
const dashboardService = {
  /**
   * Get dashboard statistics
   */
  async getDashboardStats(period: Period = 'last30days'): Promise<DashboardStats> {
    const response = await apiClient.get<DashboardStats>('/admin/dashboard', { period });
    return response.data;
  },
  
  /**
   * Get sales overview
   */
  async getSalesOverview(period: Period = 'last30days'): Promise<SalesOverview> {
    const response = await apiClient.get<SalesOverview>('/admin/analytics/sales', { period });
    return response.data;
  },
  
  /**
   * Get top selling products
   */
  async getTopSellingProducts(limit: number = 5, period: Period = 'last30days'): Promise<TopProduct[]> {
    const response = await apiClient.get<TopProduct[]>('/admin/analytics/products', { limit, period });
    return response.data;
  },
  
  /**
   * Get customer statistics
   */
  async getCustomerStats(period: Period = 'last30days'): Promise<CustomerStats> {
    const response = await apiClient.get<CustomerStats>('/admin/analytics/customers', { period });
    return response.data;
  },
  
  /**
   * Get inventory statistics
   */
  async getInventoryStats(): Promise<InventoryStats> {
    const response = await apiClient.get<InventoryStats>('/admin/analytics/inventory');
    return response.data;
  },
};

export default dashboardService;
