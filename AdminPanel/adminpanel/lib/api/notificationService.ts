export interface Notification {
  id: string
  title: string
  message: string
  read: boolean
  createdAt: string
  type: "order" | "stock" | "inquiry" | "system"
  link?: string
}

interface NotificationsResponse {
  notifications: Notification[]
  unreadCount: number
}

// Mock data for demonstration
const mockNotifications: Notification[] = [
  {
    id: "1",
    title: "New Order Received",
    message: "Order #ORD-2023-001 has been placed by Customer A",
    read: false,
    createdAt: new Date(Date.now() - 1000 * 60 * 5).toISOString(), // 5 minutes ago
    type: "order",
    link: "/orders",
  },
  {
    id: "2",
    title: "Low Stock Alert",
    message: "Kraft Paper 80gsm is running low (5 units remaining)",
    read: false,
    createdAt: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 minutes ago
    type: "stock",
    link: "/stock",
  },
  {
    id: "3",
    title: "New Inquiry",
    message: "Customer B has submitted a new inquiry about bulk orders",
    read: true,
    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(), // 2 hours ago
    type: "inquiry",
    link: "/inquiries",
  },
  {
    id: "4",
    title: "System Update",
    message: "The system will undergo maintenance on Sunday at 2:00 AM",
    read: true,
    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(), // 1 day ago
    type: "system",
  },
  {
    id: "5",
    title: "Order Status Updated",
    message: "Order #ORD-2023-002 has been shipped",
    read: true,
    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 48).toISOString(), // 2 days ago
    type: "order",
    link: "/orders",
  },
]

// In a real application, these would make API calls
export const NotificationService = {
  getNotifications: async (): Promise<NotificationsResponse> => {
    try {
      // In a real app, this would be an API call:
      // return await apiClient.get('/notifications');

      // For demo purposes, we'll return mock data
      return {
        notifications: mockNotifications,
        unreadCount: mockNotifications.filter((n) => !n.read).length,
      }
    } catch (error) {
      console.error("Error fetching notifications:", error)
      throw error
    }
  },

  markAsRead: async (id: string): Promise<void> => {
    try {
      // In a real app, this would be an API call:
      // return await apiClient.put(`/notifications/${id}/read`);

      // For demo purposes, we'll update the mock data
      const notification = mockNotifications.find((n) => n.id === id)
      if (notification) {
        notification.read = true
      }
    } catch (error) {
      console.error("Error marking notification as read:", error)
      throw error
    }
  },

  markAllAsRead: async (): Promise<void> => {
    try {
      // In a real app, this would be an API call:
      // return await apiClient.put('/notifications/read-all');

      // For demo purposes, we'll update the mock data
      mockNotifications.forEach((notification) => {
        notification.read = true
      })
    } catch (error) {
      console.error("Error marking all notifications as read:", error)
      throw error
    }
  },
}

export default NotificationService
