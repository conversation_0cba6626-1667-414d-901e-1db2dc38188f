/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    unoptimized: true,
  },
  // Add output configuration to help with client component handling
  output: 'standalone',
  // Experimental features to help with build
  experimental: {
    // Optimize build process
    optimizePackageImports: ['lucide-react', '@radix-ui/react-icons'],
  },
  // Improve handling of client components
  serverExternalPackages: [],
}

export default nextConfig
