import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(_request: NextRequest) {
  // For Firebase authentication, we rely on client-side authentication
  // since tokens are stored in localStorage and not accessible in middleware
  // All route protection is handled by the AuthProvider and layout components

  return NextResponse.next();
}

// Configure the middleware to run on specific paths
export const config = {
  matcher: [
    /*
     * Match all request paths except:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public files (public directory)
     * - api routes
     * - _vercel system paths
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.png$|api|_vercel).*)',
  ],
};
