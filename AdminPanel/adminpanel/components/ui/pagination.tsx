"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { ChevronLeft, ChevronRight, MoreHorizontal } from "lucide-react"

interface PaginationProps {
  currentPage: number
  totalPages: number
  onPageChange: (page: number) => void
  disabled?: boolean
}

export function Pagination({
  currentPage,
  totalPages,
  onPageChange,
  disabled = false,
}: PaginationProps) {
  // Don't render pagination if there's only one page
  if (totalPages <= 1) return null

  // Generate page numbers to display
  const generatePaginationItems = () => {
    const pages = []
    const displayPages = []

    // Always show first page
    displayPages.push(1)

    // Calculate range around current page
    let rangeStart = Math.max(2, currentPage - 1)
    let rangeEnd = Math.min(totalPages - 1, currentPage + 1)

    // Adjust range if we're near the start
    if (currentPage <= 3) {
      rangeEnd = Math.min(5, totalPages - 1)
    }

    // Adjust range if we're near the end
    if (currentPage >= totalPages - 2) {
      rangeStart = Math.max(2, totalPages - 4)
    }

    // Add ellipsis before range if needed
    if (rangeStart > 2) {
      displayPages.push(-1) // -1 represents ellipsis
    }

    // Add pages in the range
    for (let i = rangeStart; i <= rangeEnd; i++) {
      displayPages.push(i)
    }

    // Add ellipsis after range if needed
    if (rangeEnd < totalPages - 1) {
      displayPages.push(-2) // -2 represents ellipsis (using different key)
    }

    // Always show last page if we have more than one page
    if (totalPages > 1) {
      displayPages.push(totalPages)
    }

    // Create the button elements
    displayPages.forEach((page) => {
      if (page === -1 || page === -2) {
        // Render ellipsis
        pages.push(
          <div
            key={`ellipsis-${page}`}
            className="flex items-center justify-center h-10 w-10"
          >
            <MoreHorizontal className="h-4 w-4" />
          </div>
        )
      } else {
        // Render page button
        pages.push(
          <Button
            key={page}
            variant={page === currentPage ? "default" : "outline"}
            size="icon"
            onClick={() => onPageChange(page)}
            disabled={disabled || page === currentPage}
            className="h-10 w-10"
          >
            {page}
          </Button>
        )
      }
    })

    return pages
  }

  return (
    <div className="flex items-center justify-center space-x-2">
      {/* Previous button */}
      <Button
        variant="outline"
        size="icon"
        onClick={() => onPageChange(currentPage - 1)}
        disabled={disabled || currentPage === 1}
        className="h-10 w-10"
      >
        <ChevronLeft className="h-4 w-4" />
        <span className="sr-only">Previous page</span>
      </Button>

      {/* Page numbers */}
      {generatePaginationItems()}

      {/* Next button */}
      <Button
        variant="outline"
        size="icon"
        onClick={() => onPageChange(currentPage + 1)}
        disabled={disabled || currentPage === totalPages}
        className="h-10 w-10"
      >
        <ChevronRight className="h-4 w-4" />
        <span className="sr-only">Next page</span>
      </Button>
    </div>
  )
}
