import { TopProduct } from "@/lib/api/dashboardService";

// Default data for fallback
const defaultProducts = [
  {
    id: "1",
    name: "Kraft Paper NS 80 GSM",
    gsm: 80,
    size: "36 x 48",
    totalSold: 250,
    totalRevenue: 125000,
    percentageOfSales: 30.5
  },
  {
    id: "2",
    name: "Kraft Paper GY 120 GSM",
    gsm: 120,
    size: "36 x 48",
    totalSold: 150,
    totalRevenue: 112500,
    percentageOfSales: 27.4
  },
  {
    id: "3",
    name: "Textured Embossed Paper",
    gsm: 140,
    size: "36 x 48",
    totalSold: 100,
    totalRevenue: 85000,
    percentageOfSales: 20.7
  },
  {
    id: "4",
    name: "Cardstock 250 GSM",
    gsm: 250,
    size: "36 x 48",
    totalSold: 75,
    totalRevenue: 56250,
    percentageOfSales: 13.7
  },
  {
    id: "5",
    name: "Kraft Paper NS 100 GSM",
    gsm: 100,
    size: "36 x 48",
    totalSold: 50,
    totalRevenue: 32500,
    percentageOfSales: 7.9
  }
];

interface RecentSalesProps {
  products?: TopProduct[] | null;
}

export function RecentSales({ products }: RecentSalesProps) {
  // Use provided products or fallback to default
  const displayProducts = products || defaultProducts;

  return (
    <div className="space-y-8">
      {displayProducts.map((product) => (
        <div key={product.id} className="flex items-center">
          <div className="space-y-1">
            <p className="text-sm font-medium leading-none">{product.name}</p>
            <p className="text-sm text-muted-foreground">
              {product.gsm} GSM • {product.totalSold} rolls sold
            </p>
          </div>
          <div className="ml-auto font-medium">₹{product.totalRevenue.toLocaleString()}</div>
        </div>
      ))}
    </div>
  );
}
