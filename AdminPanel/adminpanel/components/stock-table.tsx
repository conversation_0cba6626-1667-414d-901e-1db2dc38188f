"use client"

import { useState } from "react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Edit, MoreHorizontal, Trash } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { StockFormDialog } from "@/components/stock-form-dialog"
import { DeleteStockDialog } from "@/components/delete-stock-dialog"
import { StockItem } from "@/lib/api/stockService"

interface StockTableProps {
  data: StockItem[]
  onEdit: (stock: StockItem) => void
  onDelete: (stock: StockItem) => void
  isLoading?: boolean
}

export function StockTable({ data, onEdit, onDelete, isLoading = false }: StockTableProps) {
  // Generate a simple SKU from the stock properties
  const generateSku = (stock: StockItem) => {
    return `KP-${stock.type}-${stock.gsm}-${stock.bf}`.toUpperCase();
  };

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>SKU</TableHead>
            <TableHead>Type</TableHead>
            <TableHead>GSM</TableHead>
            <TableHead>BF</TableHead>
            <TableHead>Immediate Price</TableHead>
            <TableHead>30-Day Price</TableHead>
            <TableHead>60-Day Price</TableHead>
            <TableHead>Available Rolls</TableHead>
            <TableHead>Status</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {isLoading ? (
            <TableRow>
              <TableCell colSpan={10} className="h-24 text-center">
                Loading stock data...
              </TableCell>
            </TableRow>
          ) : data.length === 0 ? (
            <TableRow>
              <TableCell colSpan={10} className="h-24 text-center">
                No stock items found.
              </TableCell>
            </TableRow>
          ) : (
            data.map((item) => (
              <TableRow key={item.id}>
                <TableCell className="font-medium">{generateSku(item)}</TableCell>
                <TableCell>{item.type}</TableCell>
                <TableCell>{item.gsm}</TableCell>
                <TableCell>{item.bf}</TableCell>
                <TableCell>₹{item.immediatePrice.toLocaleString()}</TableCell>
                <TableCell>₹{item.thirtyDayPrice.toLocaleString()}</TableCell>
                <TableCell>₹{item.sixtyDayPrice.toLocaleString()}</TableCell>
                <TableCell>{item.rollsAvailable}</TableCell>
                <TableCell>
                  {item.rollsAvailable <= 3 ? (
                    <Badge variant="destructive">Low Stock</Badge>
                  ) : (
                    <Badge variant="outline">In Stock</Badge>
                  )}
                </TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">Open menu</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuItem onClick={() => onEdit(item)}>
                        <Edit className="mr-2 h-4 w-4" />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        onClick={() => onDelete(item)}
                        className="text-destructive"
                      >
                        <Trash className="mr-2 h-4 w-4" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  )
}
