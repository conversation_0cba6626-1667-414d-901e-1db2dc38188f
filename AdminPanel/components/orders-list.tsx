"use client"

import { useState } from "react"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardFooter } from "@/components/ui/card"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Separator } from "@/components/ui/separator"
import { Check, Loader2, X } from "lucide-react"
import { format } from "date-fns"
import orderService, { type Order, type OrderStatus, type UpdateOrderStatusDto } from "@/lib/api/orderService"
import { toast } from "sonner"
import { Skeleton } from "@/components/ui/skeleton"

export function OrdersList({
  status = "all",
  orders = [],
  isLoading = false,
  onRefresh = () => {}
}) {
  const [selectedOrder, setSelectedOrder] = useState(null)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [isUpdating, setIsUpdating] = useState(false)

  // Filter orders for history tab
  const filteredOrders =
    status === "history"
      ? orders.filter((order) => ["DELIVERED", "CANCELLED"].includes(order.status))
      : status === "all"
        ? orders
        : orders

  const handleViewDetails = (order) => {
    // Use the existing order data instead of making an API call
    setSelectedOrder(order)
    setIsDialogOpen(true)
  }

  // Handle order status update
  const handleUpdateStatus = async (orderId, newStatus) => {
    setIsUpdating(true)
    try {
      const data: UpdateOrderStatusDto = {
        status: newStatus
      }
      const response = await orderService.updateOrderStatus(orderId, data)
      console.log("Status update response:", response)
      toast.success(`Order status updated to ${newStatus.toLowerCase()}`)
      setIsDialogOpen(false)
      onRefresh() // Refresh the orders list
    } catch (error) {
      console.error("Error updating order status:", error)
      toast.error("Failed to update order status. Please try again.")
    } finally {
      setIsUpdating(false)
    }
  }

  const getStatusBadge = (status) => {
    switch (status) {
      case "PENDING":
        return <Badge variant="outline" className="bg-yellow-400">Pending</Badge>
      case "APPROVED":
        return <Badge variant="secondary" className="bg-green-400">Approved</Badge>
      case "SHIPPED":
        return <Badge variant="default" className="bg-blue-400">Shipped</Badge>
      case "DELIVERED":
        return <Badge variant="success" className="bg-purple-400">Delivered</Badge>
      case "CANCELLED":
        return <Badge variant="destructive" className="bg-red-400">Cancelled</Badge>
      default:
        return <Badge variant="outline" className="bg-gray-400">Unknown</Badge>
    }
  }

  // Format date from ISO string
  const formatDate = (dateString) => {
    try {
      return format(new Date(dateString), 'dd MMM yyyy')
    } catch (error) {
      return dateString
    }
  }

  // Loading skeleton
  if (isLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {[1, 2, 3].map((i) => (
          <Card key={i} className="overflow-hidden">
            <CardContent className="p-0">
              <div className="flex items-center justify-between p-6">
                <div className="w-full">
                  <div className="flex items-center gap-2 mb-2">
                    <Skeleton className="h-5 w-32" />
                    <Skeleton className="h-5 w-20" />
                  </div>
                  <Skeleton className="h-4 w-24" />
                </div>
              </div>
              <Separator />
              <div className="p-6 space-y-2">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
                <Separator className="my-2" />
                <div className="flex justify-between">
                  <Skeleton className="h-5 w-24" />
                  <Skeleton className="h-5 w-16" />
                </div>
              </div>
            </CardContent>
            <CardFooter className="bg-muted/50 px-6 py-3">
              <Skeleton className="h-9 w-full" />
            </CardFooter>
          </Card>
        ))}
      </div>
    )
  }

  // No orders found
  if (!isLoading && filteredOrders.length === 0) {
    return (
      <div className="text-center py-10">
        <p className="text-muted-foreground">No orders found</p>
      </div>
    )
  }

  return (
    <>
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {filteredOrders.map((order) => (
          <Card key={order.id} className="overflow-hidden">
            <CardContent className="p-0">
              <div className="flex items-center justify-between p-6">
                <div>
                  <div className="flex items-center gap-2">
                    <h3 className="font-semibold">Order #{order.orderNumber || order.id.substring(0, 8)}</h3>
                    {getStatusBadge(order.status)}
                  </div>
                  <p className="text-sm text-muted-foreground">{formatDate(order.createdAt)}</p>
                  <p className="text-sm font-medium mt-1">
                    {order.user ? order.user.companyName : order.companyName}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    {order.user ? order.user.contactPerson : order.contactPerson}
                  </p>
                </div>
              </div>
              <Separator />
              <div className="p-6 space-y-2">
                {order.items && order.items.slice(0, 2).map((item, idx) => (
                  <div key={idx} className="flex justify-between text-sm">
                    <span>
                      {item.quantity}x {item.stockName || `${item.type} ${item.gsm}gsm`}
                    </span>
                    <span>₹{item.price || item.pricePerRoll}</span>
                  </div>
                ))}
                {order.items && order.items.length > 2 && (
                  <div className="text-sm text-muted-foreground">
                    + {order.items.length - 2} more items
                  </div>
                )}
                <Separator className="my-2" />
                <div className="flex justify-between font-semibold">
                  <span>Total Amount</span>
                  <span>₹{order.totalAmount}</span>
                </div>
              </div>
            </CardContent>
            <CardFooter className="bg-muted/50 px-6 py-3">
              <Button variant="outline" className="w-full" onClick={() => handleViewDetails(order)}>
                View Details &gt;
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>

      {selectedOrder && (
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle className="flex items-center justify-between">
                <span>Order #{selectedOrder.orderNumber || selectedOrder.id.substring(0, 8)}</span>
                {getStatusBadge(selectedOrder.status)}
              </DialogTitle>
              <DialogDescription>{formatDate(selectedOrder.createdAt)}</DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <h4 className="text-sm font-semibold mb-2">Items</h4>
                <div className="space-y-2">
                  {selectedOrder.items && selectedOrder.items.map((item, idx) => (
                    <div key={idx} className="flex justify-between text-sm">
                      <span>
                        {item.quantity}x {item.stockName || `${item.type} ${item.gsm}gsm`}
                      </span>
                      <span>₹{item.price || item.pricePerRoll}</span>
                    </div>
                  ))}
                  <Separator className="my-2" />
                  <div className="flex justify-between font-semibold">
                    <span>Total Amount</span>
                    <span>₹{selectedOrder.totalAmount}</span>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="text-sm font-semibold mb-2">Customer Details</h4>
                <div className="space-y-1 text-sm">
                  <p>
                    <span className="font-medium">Company:</span> {selectedOrder.user ? selectedOrder.user.companyName : selectedOrder.companyName}
                  </p>
                  <p>
                    <span className="font-medium">Contact Person:</span> {selectedOrder.user ? selectedOrder.user.contactPerson : selectedOrder.contactPerson}
                  </p>
                  <p>
                    <span className="font-medium">Contact:</span> {selectedOrder.user && selectedOrder.user.contactNumber ? selectedOrder.user.contactNumber : selectedOrder.contactNumber}
                  </p>
                  {selectedOrder.user && selectedOrder.user.email && (
                    <p>
                      <span className="font-medium">Email:</span> {selectedOrder.user.email}
                    </p>
                  )}
                  {selectedOrder.shippingAddress && (
                    <p>
                      <span className="font-medium">Shipping Address:</span>{" "}
                      {typeof selectedOrder.shippingAddress === 'string'
                        ? selectedOrder.shippingAddress
                        : `${selectedOrder.shippingAddress.addressLine1 || ''}, ${selectedOrder.shippingAddress.city || ''}, ${selectedOrder.shippingAddress.state || ''} ${selectedOrder.shippingAddress.postalCode || ''}`
                      }
                    </p>
                  )}
                </div>
              </div>

              <div>
                <h4 className="text-sm font-semibold mb-2">Order Timeline</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex items-start gap-2">
                    <div className="h-2 w-2 mt-1.5 rounded-full bg-primary"></div>
                    <div>
                      <p className="font-medium">Order Placed</p>
                      <p className="text-muted-foreground">{formatDate(selectedOrder.createdAt)}</p>
                      <p>Order #{selectedOrder.orderNumber || selectedOrder.id.substring(0, 8)} was placed</p>
                    </div>
                  </div>
                  {selectedOrder.status !== "PENDING" && selectedOrder.status !== "CANCELLED" && (
                    <div className="flex items-start gap-2">
                      <div className="h-2 w-2 mt-1.5 rounded-full bg-primary"></div>
                      <div>
                        <p className="font-medium">Order Approved</p>
                        <p className="text-muted-foreground">{selectedOrder.approvedAt ? formatDate(selectedOrder.approvedAt) : "N/A"}</p>
                        <p>Order was approved and processed</p>
                      </div>
                    </div>
                  )}
                  {(selectedOrder.status === "SHIPPED" || selectedOrder.status === "DELIVERED") && (
                    <div className="flex items-start gap-2">
                      <div className="h-2 w-2 mt-1.5 rounded-full bg-primary"></div>
                      <div>
                        <p className="font-medium">Order Shipped</p>
                        <p className="text-muted-foreground">{selectedOrder.shippedAt ? formatDate(selectedOrder.shippedAt) : "N/A"}</p>
                        <p>Order was shipped for delivery</p>
                      </div>
                    </div>
                  )}
                  {selectedOrder.status === "DELIVERED" && (
                    <div className="flex items-start gap-2">
                      <div className="h-2 w-2 mt-1.5 rounded-full bg-primary"></div>
                      <div>
                        <p className="font-medium">Order Delivered</p>
                        <p className="text-muted-foreground">{selectedOrder.deliveredAt ? formatDate(selectedOrder.deliveredAt) : "N/A"}</p>
                        <p>Order was delivered successfully</p>
                      </div>
                    </div>
                  )}
                  {selectedOrder.status === "CANCELLED" && (
                    <div className="flex items-start gap-2">
                      <div className="h-2 w-2 mt-1.5 rounded-full bg-destructive"></div>
                      <div>
                        <p className="font-medium">Order Cancelled</p>
                        <p className="text-muted-foreground">{selectedOrder.cancelledAt ? formatDate(selectedOrder.cancelledAt) : "N/A"}</p>
                        <p>Order was cancelled</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {selectedOrder.status === "PENDING" && (
              <DialogFooter>
                <div className="w-full space-y-2">
                  <div className="font-medium">Do you want to approve this order?</div>
                  <div className="flex gap-2">
                    <Button
                      className="w-full"
                      onClick={() => handleUpdateStatus(selectedOrder.id, "APPROVED")}
                      disabled={isUpdating}
                    >
                      {isUpdating ? (
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      ) : (
                        <Check className="mr-2 h-4 w-4" />
                      )}
                      Yes
                    </Button>
                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={() => handleUpdateStatus(selectedOrder.id, "CANCELLED")}
                      disabled={isUpdating}
                    >
                      {isUpdating ? (
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      ) : (
                        <X className="mr-2 h-4 w-4" />
                      )}
                      No
                    </Button>
                  </div>
                </div>
              </DialogFooter>
            )}

            {selectedOrder.status === "APPROVED" && (
              <DialogFooter>
                <div className="w-full space-y-2">
                  <div className="font-medium">Update order status:</div>
                  <div className="flex gap-2">
                    <Button
                      className="w-full"
                      onClick={() => handleUpdateStatus(selectedOrder.id, "SHIPPED")}
                      disabled={isUpdating}
                    >
                      {isUpdating ? (
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      ) : null}
                      Mark as Shipped
                    </Button>
                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={() => handleUpdateStatus(selectedOrder.id, "CANCELLED")}
                      disabled={isUpdating}
                    >
                      {isUpdating ? (
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      ) : null}
                      Cancel Order
                    </Button>
                  </div>
                </div>
              </DialogFooter>
            )}

            {selectedOrder.status === "SHIPPED" && (
              <DialogFooter>
                <div className="w-full space-y-2">
                  <Button
                    className="w-full"
                    onClick={() => handleUpdateStatus(selectedOrder.id, "DELIVERED")}
                    disabled={isUpdating}
                  >
                    {isUpdating ? (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    ) : null}
                    Mark as Delivered
                  </Button>
                </div>
              </DialogFooter>
            )}
          </DialogContent>
        </Dialog>
      )}
    </>
  )
}
