"use client"

import { useState } from "react"
import { toast } from "sonner"

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import stockService, { type StockItem } from "@/lib/api/stockService"

interface DeleteStockDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  stock: StockItem
  onSuccess: () => void
}

export function DeleteStockDialog({ open, onOpenChange, stock, onSuccess }: DeleteStockDialogProps) {
  const [isDeleting, setIsDeleting] = useState(false)

  const handleDelete = async () => {
    setIsDeleting(true)
    try {
      await stockService.deleteStockItem(stock.id)
      toast.success("Stock deleted successfully")
      onOpenChange(false)
      onSuccess()
    } catch (error) {
      console.error("Error deleting stock:", error)
      toast.error("Failed to delete stock. Please try again.")
    } finally {
      setIsDeleting(false)
    }
  }

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Are you sure you want to delete this stock?</AlertDialogTitle>
          <AlertDialogDescription>
            This will permanently delete the stock item "{stock.type} {stock.gsm} GSM BF {stock.bf}" and cannot be undone.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
          <AlertDialogAction
            onClick={(e) => {
              e.preventDefault()
              handleDelete()
            }}
            disabled={isDeleting}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {isDeleting ? "Deleting..." : "Delete"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
