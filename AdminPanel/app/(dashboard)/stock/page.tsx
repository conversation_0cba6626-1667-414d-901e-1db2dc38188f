"use client"

import type React from "react"

import { useState, useEffect, useCallback } from "react"
import { DashboardHeader } from "@/components/dashboard-header"
import { DashboardShell } from "@/components/dashboard-shell"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Filter, Plus, RefreshCw, Search, Upload } from "lucide-react"
import { StockTable } from "@/components/stock-table"
import { StockCards } from "@/components/stock-cards"
import { StockFormDialog } from "@/components/stock-form-dialog"
import { DeleteStockDialog } from "@/components/delete-stock-dialog"
import { StockImportExportDialog } from "@/components/stock-import-export-dialog"
import { StockFilterDialog } from "@/components/stock-filter-dialog"
import { DateRangePicker } from "@/components/date-range-picker"
import stockService, { type StockItem, type Stock<PERSON><PERSON>yParams } from "@/lib/api/stockService"
import { useAuth } from "@/lib/context/auth-context"
import { toast } from "sonner"
import type { DateRange } from "react-day-picker"
import { format } from "date-fns"

export default function StockPage() {
  const { user } = useAuth()
  const [stockItems, setStockItems] = useState<StockItem[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [totalPages, setTotalPages] = useState(1)
  const [totalItems, setTotalItems] = useState(0)
  const [selectedStock, setSelectedStock] = useState<StockItem | null>(null)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isImportDialogOpen, setIsImportDialogOpen] = useState(false)
  const [isFilterDialogOpen, setIsFilterDialogOpen] = useState(false)
  const [dateRange, setDateRange] = useState<DateRange | undefined>()
  const [filters, setFilters] = useState<StockQueryParams>({
    page: 1,
    limit: 10,
  })

  // Fetch stock data
  const fetchStockItems = useCallback(async () => {
    setIsLoading(true)
    try {
      const params: StockQueryParams = {
        ...filters,
        search: searchQuery,
      }

      // Add date range to params if selected
      if (dateRange?.from) {
        params.startDate = format(dateRange.from, "yyyy-MM-dd")
        if (dateRange.to) {
          params.endDate = format(dateRange.to, "yyyy-MM-dd")
        }
      }

      const response = await stockService.getAllStockItems(params)
      setStockItems(response.items)
      setTotalPages(response.pagination.totalPages)
      setTotalItems(response.pagination.totalItems)
    } catch (error) {
      console.error("Error fetching stock items:", error)
      toast.error("Failed to load stock items. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }, [filters, searchQuery, dateRange])

  // Initial data fetch
  useEffect(() => {
    fetchStockItems()
  }, [fetchStockItems])

  // Handle search
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value)
    // Reset to first page when searching
    setFilters((prev) => ({ ...prev, page: 1 }))
  }

  // Handle date range change
  const handleDateRangeChange = (range: DateRange | undefined) => {
    setDateRange(range)
    // Reset to first page when changing date range
    setFilters((prev) => ({ ...prev, page: 1 }))
  }

  // Handle edit
  const handleEdit = (stock: StockItem) => {
    setSelectedStock(stock)
    setIsEditDialogOpen(true)
  }

  // Handle delete
  const handleDelete = (stock: StockItem) => {
    setSelectedStock(stock)
    setIsDeleteDialogOpen(true)
  }

  // Get greeting based on time of day
  const getGreeting = () => {
    const hour = new Date().getHours()
    if (hour < 12) return "Good Morning"
    if (hour < 18) return "Good Afternoon"
    return "Good Evening"
  }

  return (
    <DashboardShell>
      <DashboardHeader heading="Stock Management" text={`${getGreeting()}, ${user?.contactPerson || "Admin"}`}>
        <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2">
          <div className="relative w-full sm:w-auto">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search stock..."
              className="w-full pl-8 sm:w-[200px] md:w-[300px]"
              value={searchQuery}
              onChange={handleSearch}
            />
          </div>
          <div className="flex space-x-2">
            <Button onClick={() => setIsImportDialogOpen(true)} className="whitespace-nowrap">
              <Upload className="mr-2 h-4 w-4" />
              Import/Export
            </Button>
            <Button onClick={() => setIsAddDialogOpen(true)} className="whitespace-nowrap">
              <Plus className="mr-2 h-4 w-4" />
              Add Stock
            </Button>
          </div>
        </div>
      </DashboardHeader>

      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <h2 className="text-lg font-semibold">
          Available Kraft Paper: <span className="text-muted-foreground text-sm ml-2">({totalItems} items)</span>
        </h2>
        <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2">
          <DateRangePicker
            onChange={handleDateRangeChange}
            placeholder="Filter by date"
          />
          <Button variant="outline" size="sm" onClick={fetchStockItems} className="whitespace-nowrap">
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh
          </Button>
          <Button variant="outline" size="sm" onClick={() => setIsFilterDialogOpen(true)} className="whitespace-nowrap">
            <Filter className="mr-2 h-4 w-4" />
            Filters
            {Object.keys(filters).some((key) => ["type", "gsm", "bf"].includes(key)) && (
              <span className="ml-1 rounded-full bg-primary w-2 h-2" />
            )}
          </Button>
        </div>
      </div>

      <Tabs defaultValue="table" className="space-y-4">
        <TabsList>
          <TabsTrigger value="table">Table View</TabsTrigger>
          <TabsTrigger value="cards">Card View</TabsTrigger>
        </TabsList>
        <TabsContent value="table" className="space-y-4">
          <StockTable data={stockItems} onEdit={handleEdit} onDelete={handleDelete} isLoading={isLoading} />
        </TabsContent>
        <TabsContent value="cards" className="space-y-4">
          <StockCards data={stockItems} onEdit={handleEdit} onDelete={handleDelete} isLoading={isLoading} />
        </TabsContent>
      </Tabs>

      {/* Pagination controls */}
      <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:justify-between sm:space-y-0 mt-4">
        <div className="text-sm text-muted-foreground">
          Showing {stockItems.length} of {totalItems} items
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setFilters((prev) => ({ ...prev, page: Math.max(1, prev.page! - 1) }))}
            disabled={filters.page === 1 || isLoading}
          >
            Previous
          </Button>
          <div className="text-sm">
            Page {filters.page} of {totalPages}
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setFilters((prev) => ({ ...prev, page: prev.page! + 1 }))}
            disabled={filters.page === totalPages || isLoading}
          >
            Next
          </Button>
        </div>
      </div>

      {/* Add Stock Dialog */}
      <StockFormDialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen} onSuccess={fetchStockItems} />

      {/* Edit Stock Dialog */}
      {selectedStock && (
        <StockFormDialog
          open={isEditDialogOpen}
          onOpenChange={setIsEditDialogOpen}
          stock={selectedStock}
          onSuccess={fetchStockItems}
        />
      )}

      {/* Delete Stock Dialog */}
      {selectedStock && (
        <DeleteStockDialog
          open={isDeleteDialogOpen}
          onOpenChange={setIsDeleteDialogOpen}
          stock={selectedStock}
          onSuccess={fetchStockItems}
        />
      )}

      {/* Stock Import/Export Dialog */}
      <StockImportExportDialog
        open={isImportDialogOpen}
        onOpenChange={setIsImportDialogOpen}
        onSuccess={fetchStockItems}
      />

      {/* Filter Dialog */}
      <StockFilterDialog
        open={isFilterDialogOpen}
        onOpenChange={setIsFilterDialogOpen}
        currentFilters={filters}
        onApplyFilters={setFilters}
      />
    </DashboardShell>
  )
}
