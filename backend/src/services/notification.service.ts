import prisma from '../config/database';
import { NotificationType } from '@prisma/client';
import { AppError } from '../utils/errorHandler';

/**
 * Create a new notification
 * @param userId - Rider ID to create notification for (can be null for system notifications)
 * @param type - Notification type
 * @param title - Notification title
 * @param message - Notification message
 * @returns Created notification
 */
export const createNotification = async (
  userId: string | null,
  type: NotificationType,
  title: string,
  message: string
) => {
  try {
    // If userId is null, find the first admin user or any approved user to attach the notification to
    if (userId === null) {
      // Find any approved rider to attach the notification to
      const rider = await prisma.rider.findFirst({
        where: {
          isApproved: true,
        },
        select: {
          id: true,
        },
      });
      
      if (!rider) {
        console.error('No approved rider found to attach system notification to');
        
        // As a fallback, get any rider from the system
        const anyRider = await prisma.rider.findFirst({
          select: {
            id: true,
          },
        });
        
        if (!anyRider) {
          console.error('No riders found in the system to attach notification to');
          return null;
        }
        
        userId = anyRider.id;
      } else {
        userId = rider.id;
      }
      
      // Mark this as a system notification by updating the title
      title = `[SYSTEM] ${title}`;
    }
    
    // Create notification with proper rider relation
    const notification = await prisma.notification.create({
      data: {
        type,
        title,
        message,
        // Connect the notification to the rider using the relation
        rider: {
          connect: {
            id: userId
          }
        }
      },
    });

    return notification;
  } catch (error) {
    console.error('Error creating notification:', error);
    // Just log the error but don't throw it to prevent API failures
    return null;
  }
};

/**
 * Get rider notifications with pagination
 * @param userId - Rider ID to get notifications for
 * @param page - Page number (default: 1)
 * @param limit - Number of items per page (default: 10)
 * @param isRead - Filter by read status (optional)
 * @returns Paginated notifications
 */
export const getUserNotifications = async (
  userId: string,
  page = 1,
  limit = 10,
  isRead?: boolean
) => {
  const skip = (page - 1) * limit;

  // Build where clause
  const where: any = { userId };
  if (isRead !== undefined) {
    where.isRead = isRead;
  }

  // Get notifications with pagination
  const [notifications, total] = await Promise.all([
    prisma.notification.findMany({
      where,
      orderBy: { createdAt: 'desc' },
      skip,
      take: limit,
    }),
    prisma.notification.count({ where }),
  ]);

  // Calculate pagination info
  const totalPages = Math.ceil(total / limit);
  const hasNextPage = page < totalPages;
  const hasPrevPage = page > 1;

  return {
    notifications,
    pagination: {
      total,
      page,
      limit,
      totalPages,
      hasNextPage,
      hasPrevPage,
    },
  };
};

/**
 * Mark notification as read
 * @param notificationId - Notification ID
 * @param userId - Rider ID (for authorization)
 * @param isAdminUser - Whether the requester is an admin user (optional)
 * @returns Updated notification
 */
export const markNotificationAsRead = async (notificationId: string, userId: string, isAdminUser?: boolean) => {
  // Build query conditions
  const whereCondition: any = {
    id: notificationId,
  };
  
  // If not admin, ensure rider can only access their own notifications
  if (!isAdminUser) {
    whereCondition.userId = userId;
  } else if (isAdminUser) {
    // Admin can read any notification, especially those with admin prefix
    // If necessary, we could add a title check here
  }
  
  // Find notification
  const notification = await prisma.notification.findFirst({
    where: whereCondition,
  });

  if (!notification) {
    throw new AppError('Notification not found', 404);
  }

  // If already read, return as is
  if (notification.isRead) {
    return notification;
  }

  // Update notification
  return prisma.notification.update({
    where: { id: notificationId },
    data: {
      isRead: true,
      readAt: new Date(),
    },
  });
};

/**
 * Mark all notifications as read for a rider
 * @param userId - Rider ID
 * @returns Count of updated notifications
 */
export const markAllNotificationsAsRead = async (userId: string) => {
  // Update all unread notifications for the user
  const result = await prisma.notification.updateMany({
    where: {
      userId,
      isRead: false,
    },
    data: {
      isRead: true,
      readAt: new Date(),
    },
  });

  return { count: result.count };
};

/**
 * Delete a notification
 * @param notificationId - Notification ID
 * @param userId - Rider ID (for authorization)
 * @param isAdminUser - Whether the requester is an admin user (optional)
 * @returns Deleted notification
 */
export const deleteNotification = async (notificationId: string, userId: string, isAdminUser?: boolean) => {
  // Build query conditions
  const whereCondition: any = {
    id: notificationId,
  };
  
  // If not admin, ensure rider can only access their own notifications
  if (!isAdminUser) {
    whereCondition.userId = userId;
  } else if (isAdminUser) {
    // Admin can delete any notification, especially those with admin prefix
    // If necessary, we could add a title check here
  }
  
  // Find notification
  const notification = await prisma.notification.findFirst({
    where: whereCondition,
  });

  if (!notification) {
    throw new AppError('Notification not found', 404);
  }

  // Delete notification
  return prisma.notification.delete({
    where: { id: notificationId },
  });
};

/**
 * Create account approval notification
 * @param userId - Rider ID
 * @returns Created notification
 */
export const createAccountApprovalNotification = async (userId: string) => {
  return createNotification(
    userId,
    'ACCOUNT_APPROVAL',
    'Account Approved',
    'Your account has been approved. You can now place orders.'
  );
};

/**
 * Create account rejection notification
 * @param userId - Rider ID
 * @param reason - Rejection reason
 * @returns Created notification
 */
export const createAccountRejectionNotification = async (userId: string, reason: string) => {
  return createNotification(
    userId,
    'ACCOUNT_APPROVAL',
    'Account Application Status',
    `Your account application has not been approved. Reason: ${reason}`
  );
};

/**
 * Create a system notification for admin users
 * With the new Firebase authentication setup, admin users don't have 
 * traditional DB records. This function stores admin notifications in 
 * a special "system" category that admin interfaces can retrieve.
 * 
 * @param type - Notification type
 * @param title - Notification title
 * @param message - Notification message
 * @returns Created notification
 */
export const createSystemAdminNotification = async (
  type: NotificationType,
  title: string,
  message: string
) => {
  try {
    // Use the enhanced createNotification function that handles null userIds
    return createNotification(
      null,
      type,
      `[ADMIN] ${title}`, // Already prefixing with [ADMIN] here
      message
    );
  } catch (error) {
    console.error('Error creating system admin notification:', error);
    // Just log the error but don't throw it to prevent API failures
    return null;
  }
};
/**
 * Get system admin notifications with pagination
 * This function retrieves notifications specifically designated for the system admin
 * Used by the admin interface to display notifications related to system events
 * 
 * @param page - Page number (default: 1)
 * @param limit - Number of items per page (default: 10)
 * @param isRead - Filter by read status (optional)
 * @returns Paginated system admin notifications
 */
export const getSystemAdminNotifications = async (
  page = 1,
  limit = 10,
  isRead?: boolean
) => {
  const skip = (page - 1) * limit;

  // Build where clause - look for the special title prefix
  const where: any = { 
    title: {
      startsWith: '[ADMIN]'
    }
  };
  
  if (isRead !== undefined) {
    where.isRead = isRead;
  }

  // Get notifications with pagination
  const [notifications, total] = await Promise.all([
    prisma.notification.findMany({
      where,
      orderBy: { createdAt: 'desc' },
      skip,
      take: limit,
    }),
    prisma.notification.count({ where }),
  ]);

  // Calculate pagination info
  const totalPages = Math.ceil(total / limit);
  const hasNextPage = page < totalPages;
  const hasPrevPage = page > 1;

  return {
    notifications,
    pagination: {
      total,
      page,
      limit,
      totalPages,
      hasNextPage,
      hasPrevPage,
    },
  };
};
