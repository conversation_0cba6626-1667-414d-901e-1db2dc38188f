import apiClient from "./apiClient"

export interface Notification {
  id: string
  title: string
  message: string
  read: boolean
  createdAt: string
  type: "order" | "stock" | "inquiry" | "system" | "ACCOUNT_APPROVAL" | "ORDER_STATUS_UPDATE" | "ENQUIRY_RESPONSE" | "STOCK_ALERT"
  link?: string
  isRead?: boolean
}

interface NotificationsResponse {
  notifications: Notification[]
  unreadCount: number
  total?: number
  page?: number
  limit?: number
}

// Transform backend notification to frontend format
const transformNotification = (backendNotification: any): Notification => {
  return {
    id: backendNotification.id,
    title: backendNotification.title,
    message: backendNotification.message,
    read: backendNotification.isRead || false,
    createdAt: backendNotification.createdAt,
    type: backendNotification.type?.toLowerCase() || "system",
    isRead: backendNotification.isRead || false,
  }
}

export const NotificationService = {
  getNotifications: async (page = 1, limit = 10, unreadOnly = false): Promise<NotificationsResponse> => {
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        ...(unreadOnly && { unreadOnly: 'true' })
      })

      const response = await apiClient.get(`/notifications/admin?${params}`)

      if (response.data?.status === 'success') {
        const notifications = response.data.data.notifications.map(transformNotification)
        return {
          notifications,
          unreadCount: notifications.filter(n => !n.read).length,
          total: response.data.data.total,
          page: response.data.data.page,
          limit: response.data.data.limit,
        }
      }

      throw new Error('Invalid response format')
    } catch (error) {
      console.error("Error fetching notifications:", error)
      // Return empty data on error instead of throwing
      return {
        notifications: [],
        unreadCount: 0,
        total: 0,
        page: 1,
        limit: 10,
      }
    }
  },

  markAsRead: async (id: string): Promise<void> => {
    try {
      await apiClient.patch(`/notifications/admin/${id}/read`)
    } catch (error) {
      console.error("Error marking notification as read:", error)
      throw error
    }
  },

  markAllAsRead: async (): Promise<void> => {
    try {
      await apiClient.patch('/notifications/read-all')
    } catch (error) {
      console.error("Error marking all notifications as read:", error)
      throw error
    }
  },

  deleteNotification: async (id: string): Promise<void> => {
    try {
      await apiClient.delete(`/notifications/admin/${id}`)
    } catch (error) {
      console.error("Error deleting notification:", error)
      throw error
    }
  },
}

export default NotificationService
